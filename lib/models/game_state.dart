enum GameState {
  idle,
  playing,
  paused,
  gameOver,
  loading,
}

extension GameStateExtension on GameState {
  String get displayName {
    switch (this) {
      case GameState.idle:
        return 'Ready to Play';
      case GameState.playing:
        return 'Playing';
      case GameState.paused:
        return 'Paused';
      case GameState.gameOver:
        return 'Game Over';
      case GameState.loading:
        return 'Loading';
    }
  }

  bool get isPlaying => this == GameState.playing;
  bool get isPaused => this == GameState.paused;
  bool get isGameOver => this == GameState.gameOver;
  bool get isIdle => this == GameState.idle;
  bool get isLoading => this == GameState.loading;
}
