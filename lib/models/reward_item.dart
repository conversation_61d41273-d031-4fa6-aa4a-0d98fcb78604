enum RewardType {
  skin,
  background,
  achievement,
}

class RewardItem {
  final String id;
  final String name;
  final String description;
  final RewardType type;
  final int cost;
  bool isUnlocked;
  final String? imagePath;

  RewardItem({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.cost,
    this.isUnlocked = false,
    this.imagePath,
  });

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.toString(),
      'cost': cost,
      'isUnlocked': isUnlocked,
      'imagePath': imagePath,
    };
  }

  // Create from JSON
  factory RewardItem.fromJson(Map<String, dynamic> json) {
    return RewardItem(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      type: RewardType.values.firstWhere(
        (e) => e.toString() == json['type'],
      ),
      cost: json['cost'],
      isUnlocked: json['isUnlocked'] ?? false,
      imagePath: json['imagePath'],
    );
  }

  @override
  String toString() {
    return 'RewardItem(id: $id, name: $name, type: $type, cost: $cost, unlocked: $isUnlocked)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RewardItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
