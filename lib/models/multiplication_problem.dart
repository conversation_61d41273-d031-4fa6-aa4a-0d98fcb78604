import 'dart:math';

class MultiplicationProblem {
  final int firstNumber;
  final int secondNumber;
  final int answer;
  final DateTime createdAt;

  MultiplicationProblem({
    required this.firstNumber,
    required this.secondNumber,
    required this.answer,
    required this.createdAt,
  });

  // Create a random multiplication problem
  factory MultiplicationProblem.random(int maxNumber) {
    final random = Random();
    final first = random.nextInt(maxNumber) + 1;
    final second = random.nextInt(maxNumber) + 1;
    
    return MultiplicationProblem(
      firstNumber: first,
      secondNumber: second,
      answer: first * second,
      createdAt: DateTime.now(),
    );
  }

  // Create a specific multiplication problem
  factory MultiplicationProblem.create(int first, int second) {
    return MultiplicationProblem(
      firstNumber: first,
      secondNumber: second,
      answer: first * second,
      createdAt: DateTime.now(),
    );
  }

  // Create a problem for a specific times table
  factory MultiplicationProblem.forTable(int table, int maxMultiplier) {
    final random = Random();
    final multiplier = random.nextInt(maxMultiplier) + 1;
    
    return MultiplicationProblem(
      firstNumber: table,
      secondNumber: multiplier,
      answer: table * multiplier,
      createdAt: DateTime.now(),
    );
  }

  // Get the problem as a string
  String get problemText => '$firstNumber × $secondNumber';
  
  // Get the problem with answer as a string
  String get fullProblemText => '$firstNumber × $secondNumber = $answer';

  // Check if a given answer is correct
  bool isCorrect(int userAnswer) => userAnswer == answer;

  // Get difficulty level based on the numbers involved
  int get difficultyLevel {
    final maxNum = max(firstNumber, secondNumber);
    if (maxNum <= 5) return 1;
    if (maxNum <= 8) return 2;
    if (maxNum <= 10) return 3;
    return 4;
  }

  // Get all possible wrong answers for multiple choice
  List<int> getMultipleChoiceOptions({int numberOfOptions = 4}) {
    final options = <int>[answer];
    final random = Random();
    
    while (options.length < numberOfOptions) {
      // Generate wrong answers that are close to the correct answer
      int wrongAnswer;
      final variation = random.nextInt(20) + 1; // 1-20
      
      if (random.nextBool()) {
        wrongAnswer = answer + variation;
      } else {
        wrongAnswer = max(1, answer - variation);
      }
      
      // Avoid duplicates
      if (!options.contains(wrongAnswer)) {
        options.add(wrongAnswer);
      }
    }
    
    // Shuffle the options
    options.shuffle();
    return options;
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'firstNumber': firstNumber,
      'secondNumber': secondNumber,
      'answer': answer,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  // Create from JSON
  factory MultiplicationProblem.fromJson(Map<String, dynamic> json) {
    return MultiplicationProblem(
      firstNumber: json['firstNumber'],
      secondNumber: json['secondNumber'],
      answer: json['answer'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  @override
  String toString() {
    return 'MultiplicationProblem($firstNumber × $secondNumber = $answer)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MultiplicationProblem &&
        other.firstNumber == firstNumber &&
        other.secondNumber == secondNumber &&
        other.answer == answer;
  }

  @override
  int get hashCode {
    return firstNumber.hashCode ^ secondNumber.hashCode ^ answer.hashCode;
  }
}
