import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../utils/app_theme.dart';
import '../utils/app_routes.dart';
import '../providers/rewards_provider.dart';
import '../providers/settings_provider.dart';
import '../models/reward_item.dart';

class RewardsScreen extends StatefulWidget {
  const RewardsScreen({super.key});

  @override
  State<RewardsScreen> createState() => _RewardsScreenState();
}

class _RewardsScreenState extends State<RewardsScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _purchaseController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _purchaseController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    _purchaseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: context.watch<SettingsProvider>().isDarkMode
            ? AppTheme.nightJungleGradient
            : AppTheme.jungleGradient,
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              _buildStatsBar(),
              _buildTabBar(),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildProgressTab(),
                    _buildShopTab(),
                    _buildAchievementsTab(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            onPressed: () => AppRoutes.goBack(context),
            icon: const Icon(
              Icons.arrow_back,
              color: AppTheme.white,
              size: 28,
            ),
          ),
          Text(
            'My Rewards',
            style: AppTheme.headingStyle.copyWith(
              color: AppTheme.white,
              fontSize: 28,
            ),
          ),
          const SizedBox(width: 48),
        ],
      ),
    );
  }

  Widget _buildStatsBar() {
    return Consumer<RewardsProvider>(
      builder: (context, rewardsProvider, child) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 20),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppTheme.white.withValues(alpha: 0.95),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildStatItem(
                icon: Icons.star,
                label: 'Bananas',
                value: '${rewardsProvider.bananas}',
                color: AppTheme.bananaYellow,
              ),
              _buildStatItem(
                icon: Icons.emoji_events,
                label: 'Stars',
                value: '${rewardsProvider.stars}',
                color: AppTheme.jungleGreen,
              ),
              _buildStatItem(
                icon: Icons.trending_up,
                label: 'Score',
                value: '${rewardsProvider.totalScore}',
                color: AppTheme.earthBrown,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: AppTheme.white,
            size: 24,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: AppTheme.titleStyle.copyWith(
            color: AppTheme.earthBrown,
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
        Text(
          label,
          style: AppTheme.bodyStyle.copyWith(
            color: AppTheme.earthBrown,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(16),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: AppTheme.jungleGreen,
          borderRadius: BorderRadius.circular(16),
        ),
        labelColor: AppTheme.white,
        unselectedLabelColor: AppTheme.earthBrown,
        labelStyle: AppTheme.bodyStyle.copyWith(fontWeight: FontWeight.bold),
        tabs: const [
          Tab(text: 'Progress'),
          Tab(text: 'Shop'),
          Tab(text: 'Achievements'),
        ],
      ),
    );
  }

  Widget _buildProgressTab() {
    return Consumer<RewardsProvider>(
      builder: (context, rewardsProvider, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildProgressCard(
                title: 'Daily Progress',
                value: rewardsProvider.getTodayProgress(),
                maxValue: 100,
                icon: Icons.today,
                color: AppTheme.bananaYellow,
              ),

              const SizedBox(height: 20),

              _buildProgressCard(
                title: 'Total Score',
                value: rewardsProvider.totalScore,
                maxValue: 1000,
                icon: Icons.trending_up,
                color: AppTheme.jungleGreen,
              ),

              const SizedBox(height: 20),

              _buildProgressCard(
                title: 'Unlocked Items',
                value: rewardsProvider.unlockedSkins.length + rewardsProvider.unlockedBackgrounds.length,
                maxValue: 10,
                icon: Icons.lock_open,
                color: AppTheme.earthBrown,
              ),

              const SizedBox(height: 30),

              Text(
                'Recent Achievements',
                style: AppTheme.titleStyle.copyWith(
                  color: AppTheme.white,
                  fontSize: 22,
                ),
              ),

              const SizedBox(height: 16),

              _buildAchievementsList(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildProgressCard({
    required String title,
    required int value,
    required int maxValue,
    required IconData icon,
    required Color color,
  }) {
    final progress = (value / maxValue).clamp(0.0, 1.0);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.white.withValues(alpha: 0.95),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  color: AppTheme.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: AppTheme.titleStyle.copyWith(
                        color: AppTheme.earthBrown,
                        fontSize: 18,
                      ),
                    ),
                    Text(
                      '$value / $maxValue',
                      style: AppTheme.bodyStyle.copyWith(
                        color: AppTheme.earthBrown,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              Text(
                '${(progress * 100).round()}%',
                style: AppTheme.titleStyle.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: color.withValues(alpha: 0.2),
            valueColor: AlwaysStoppedAnimation<Color>(color),
            minHeight: 8,
          ),
        ],
      ),
    );
  }

  Widget _buildShopTab() {
    return Consumer<RewardsProvider>(
      builder: (context, rewardsProvider, child) {
        final availableRewards = rewardsProvider.availableRewards;

        return SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Monkey Skins',
                style: AppTheme.titleStyle.copyWith(
                  color: AppTheme.white,
                  fontSize: 22,
                ),
              ),

              const SizedBox(height: 16),

              GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  childAspectRatio: 0.8,
                ),
                itemCount: availableRewards.where((r) => r.type == RewardType.skin).length,
                itemBuilder: (context, index) {
                  final skins = availableRewards.where((r) => r.type == RewardType.skin).toList();
                  return _buildRewardCard(skins[index], rewardsProvider);
                },
              ),

              const SizedBox(height: 30),

              Text(
                'Backgrounds',
                style: AppTheme.titleStyle.copyWith(
                  color: AppTheme.white,
                  fontSize: 22,
                ),
              ),

              const SizedBox(height: 16),

              GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  childAspectRatio: 0.8,
                ),
                itemCount: availableRewards.where((r) => r.type == RewardType.background).length,
                itemBuilder: (context, index) {
                  final backgrounds = availableRewards.where((r) => r.type == RewardType.background).toList();
                  return _buildRewardCard(backgrounds[index], rewardsProvider);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildRewardCard(RewardItem reward, RewardsProvider rewardsProvider) {
    final canPurchase = rewardsProvider.canPurchase(reward.id);
    final isUnlocked = reward.isUnlocked;

    return Container(
      decoration: BoxDecoration(
        color: AppTheme.white.withValues(alpha: 0.95),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isUnlocked ? AppTheme.leafGreen : AppTheme.jungleGreen,
          width: isUnlocked ? 3 : 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Reward image/icon
          Expanded(
            flex: 3,
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: isUnlocked ? AppTheme.leafGreen : AppTheme.lightJungleGreen,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Center(
                child: Icon(
                  _getRewardIcon(reward),
                  size: 48,
                  color: isUnlocked ? AppTheme.white : AppTheme.earthBrown,
                ),
              ),
            ),
          ),

          // Reward info
          Expanded(
            flex: 2,
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    reward.name,
                    style: AppTheme.bodyStyle.copyWith(
                      color: AppTheme.earthBrown,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    reward.description,
                    style: AppTheme.bodyStyle.copyWith(
                      color: AppTheme.earthBrown,
                      fontSize: 12,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const Spacer(),
                  if (isUnlocked)
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(vertical: 6),
                      decoration: BoxDecoration(
                        color: AppTheme.leafGreen,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        'UNLOCKED',
                        textAlign: TextAlign.center,
                        style: AppTheme.bodyStyle.copyWith(
                          color: AppTheme.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    )
                  else
                    GestureDetector(
                      onTap: canPurchase ? () => _purchaseReward(reward, rewardsProvider) : null,
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(vertical: 6),
                        decoration: BoxDecoration(
                          color: canPurchase ? AppTheme.bananaYellow : AppTheme.lightEarthBrown,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.star,
                              size: 16,
                              color: canPurchase ? AppTheme.earthBrown : AppTheme.earthBrown.withValues(alpha: 0.5),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${reward.cost}',
                              style: AppTheme.bodyStyle.copyWith(
                                color: canPurchase ? AppTheme.earthBrown : AppTheme.earthBrown.withValues(alpha: 0.5),
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAchievementsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Achievements',
            style: AppTheme.titleStyle.copyWith(
              color: AppTheme.white,
              fontSize: 22,
            ),
          ),

          const SizedBox(height: 16),

          _buildAchievementsList(),
        ],
      ),
    );
  }

  Widget _buildAchievementsList() {
    final achievements = [
      {'title': 'First Steps', 'description': 'Complete your first multiplication', 'icon': Icons.baby_changing_station, 'unlocked': true},
      {'title': 'Quick Learner', 'description': 'Get 5 correct answers in a row', 'icon': Icons.flash_on, 'unlocked': true},
      {'title': 'Math Master', 'description': 'Reach level 3', 'icon': Icons.school, 'unlocked': false},
      {'title': 'Perfectionist', 'description': 'Get 100% accuracy in practice mode', 'icon': Icons.star, 'unlocked': false},
      {'title': 'Dedicated Student', 'description': 'Practice for 7 days in a row', 'icon': Icons.calendar_today, 'unlocked': false},
    ];

    return Column(
      children: achievements.map((achievement) {
        final isUnlocked = achievement['unlocked'] as bool;

        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppTheme.white.withValues(alpha: 0.95),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: isUnlocked ? AppTheme.bananaYellow : AppTheme.lightEarthBrown,
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: isUnlocked ? AppTheme.bananaYellow : AppTheme.lightEarthBrown,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  achievement['icon'] as IconData,
                  color: isUnlocked ? AppTheme.earthBrown : AppTheme.earthBrown.withValues(alpha: 0.5),
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      achievement['title'] as String,
                      style: AppTheme.titleStyle.copyWith(
                        color: isUnlocked ? AppTheme.earthBrown : AppTheme.earthBrown.withValues(alpha: 0.7),
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      achievement['description'] as String,
                      style: AppTheme.bodyStyle.copyWith(
                        color: isUnlocked ? AppTheme.earthBrown : AppTheme.earthBrown.withValues(alpha: 0.5),
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              if (isUnlocked)
                const Icon(
                  Icons.check_circle,
                  color: AppTheme.leafGreen,
                  size: 24,
                ),
            ],
          ),
        );
      }).toList(),
    );
  }

  IconData _getRewardIcon(RewardItem reward) {
    switch (reward.id) {
      case 'pirate_hat':
        return Icons.sailing;
      case 'wizard_hat':
        return Icons.auto_fix_high;
      case 'crown':
        return Icons.diamond;
      case 'superhero_cape':
        return Icons.flash_on;
      case 'jungle_night':
        return Icons.nightlight;
      case 'beach_paradise':
        return Icons.beach_access;
      case 'space_adventure':
        return Icons.rocket_launch;
      default:
        return Icons.emoji_events;
    }
  }

  void _purchaseReward(RewardItem reward, RewardsProvider rewardsProvider) async {
    final success = await rewardsProvider.purchaseReward(reward.id);

    if (success) {
      _purchaseController.forward().then((_) {
        _purchaseController.reverse();
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${reward.name} unlocked!'),
            backgroundColor: AppTheme.leafGreen,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Not enough bananas!'),
            backgroundColor: AppTheme.earthBrown,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }
}
