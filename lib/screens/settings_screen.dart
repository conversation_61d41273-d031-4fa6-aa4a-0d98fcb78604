import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../utils/app_theme.dart';
import '../utils/app_routes.dart';
import '../providers/settings_provider.dart';
import '../services/audio_service.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final AudioService _audioService = AudioService();

  @override
  void initState() {
    super.initState();
    _initializeAudio();
  }

  Future<void> _initializeAudio() async {
    await _audioService.initialize();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: context.watch<SettingsProvider>().isDarkMode
            ? AppTheme.nightJungleGradient
            : AppTheme.jungleGradient,
        child: <PERSON><PERSON><PERSON>(
          child: Column(
            children: [
              _buildHeader(),
              Expanded(
                child: _buildSettingsList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            onPressed: () => AppRoutes.goBack(context),
            icon: const Icon(
              Icons.arrow_back,
              color: AppTheme.white,
              size: 28,
            ),
          ),
          Text(
            'Settings',
            style: AppTheme.headingStyle.copyWith(
              color: AppTheme.white,
              fontSize: 28,
            ),
          ),
          const SizedBox(width: 48),
        ],
      ),
    );
  }

  Widget _buildSettingsList() {
    return Consumer<SettingsProvider>(
      builder: (context, settingsProvider, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSectionTitle('Audio Settings'),
              _buildSettingsCard([
                _buildSwitchSetting(
                  title: 'Sound Effects',
                  subtitle: 'Enable button sounds and feedback',
                  icon: Icons.volume_up,
                  value: settingsProvider.soundEnabled,
                  onChanged: (value) async {
                    await settingsProvider.toggleSound();
                    await _audioService.updateSettings(soundEnabled: value);
                  },
                ),
                _buildDivider(),
                _buildVoiceGenderSetting(settingsProvider),
                _buildDivider(),
                _buildVoiceSpeedSetting(settingsProvider),
              ]),

              const SizedBox(height: 30),

              _buildSectionTitle('Appearance'),
              _buildSettingsCard([
                _buildSwitchSetting(
                  title: 'Dark Mode',
                  subtitle: 'Switch to night jungle theme',
                  icon: Icons.dark_mode,
                  value: settingsProvider.isDarkMode,
                  onChanged: (value) => settingsProvider.toggleDarkMode(),
                ),
              ]),

              const SizedBox(height: 30),

              _buildSectionTitle('Game Settings'),
              _buildSettingsCard([
                _buildLanguageSetting(settingsProvider),
              ]),

              const SizedBox(height: 30),

              _buildSectionTitle('About'),
              _buildSettingsCard([
                _buildInfoSetting(
                  title: 'Version',
                  subtitle: '1.0.0',
                  icon: Icons.info,
                ),
                _buildDivider(),
                _buildActionSetting(
                  title: 'Reset Progress',
                  subtitle: 'Clear all game data',
                  icon: Icons.refresh,
                  onTap: () => _showResetDialog(),
                ),
              ]),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        title,
        style: AppTheme.titleStyle.copyWith(
          color: AppTheme.white,
          fontSize: 22,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildSettingsCard(List<Widget> children) {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.white.withValues(alpha: 0.95),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: children,
      ),
    );
  }

  Widget _buildSwitchSetting({
    required String title,
    required String subtitle,
    required IconData icon,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: AppTheme.jungleGreen,
          shape: BoxShape.circle,
        ),
        child: Icon(
          icon,
          color: AppTheme.white,
          size: 20,
        ),
      ),
      title: Text(
        title,
        style: AppTheme.titleStyle.copyWith(
          color: AppTheme.earthBrown,
          fontSize: 16,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: AppTheme.bodyStyle.copyWith(
          color: AppTheme.earthBrown.withValues(alpha: 0.7),
          fontSize: 14,
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: AppTheme.leafGreen,
        activeTrackColor: AppTheme.lightJungleGreen,
        inactiveThumbColor: AppTheme.lightEarthBrown,
        inactiveTrackColor: AppTheme.lightEarthBrown.withValues(alpha: 0.3),
      ),
    );
  }

  Widget _buildDivider() {
    return Divider(
      color: AppTheme.lightJungleGreen.withValues(alpha: 0.3),
      height: 1,
      thickness: 1,
    );
  }

  Widget _buildVoiceGenderSetting(SettingsProvider settingsProvider) {
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: AppTheme.bananaYellow,
          shape: BoxShape.circle,
        ),
        child: Icon(
          settingsProvider.voiceGender == 'female' ? Icons.person : Icons.person_outline,
          color: AppTheme.earthBrown,
          size: 20,
        ),
      ),
      title: Text(
        'Voice Gender',
        style: AppTheme.titleStyle.copyWith(
          color: AppTheme.earthBrown,
          fontSize: 16,
        ),
      ),
      subtitle: Text(
        'Choose voice for speech feedback',
        style: AppTheme.bodyStyle.copyWith(
          color: AppTheme.earthBrown.withValues(alpha: 0.7),
          fontSize: 14,
        ),
      ),
      trailing: DropdownButton<String>(
        value: settingsProvider.voiceGender,
        onChanged: (String? newValue) async {
          if (newValue != null) {
            await settingsProvider.setVoiceGender(newValue);
            await _audioService.updateSettings(voiceGender: newValue);
          }
        },
        items: const [
          DropdownMenuItem(value: 'female', child: Text('Female')),
          DropdownMenuItem(value: 'male', child: Text('Male')),
        ],
        underline: Container(),
        style: AppTheme.bodyStyle.copyWith(color: AppTheme.earthBrown),
      ),
    );
  }

  Widget _buildVoiceSpeedSetting(SettingsProvider settingsProvider) {
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: AppTheme.earthBrown,
          shape: BoxShape.circle,
        ),
        child: const Icon(
          Icons.speed,
          color: AppTheme.white,
          size: 20,
        ),
      ),
      title: Text(
        'Voice Speed',
        style: AppTheme.titleStyle.copyWith(
          color: AppTheme.earthBrown,
          fontSize: 16,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Adjust speech speed',
            style: AppTheme.bodyStyle.copyWith(
              color: AppTheme.earthBrown.withValues(alpha: 0.7),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          Slider(
            value: settingsProvider.voiceSpeed,
            min: 0.5,
            max: 2.0,
            divisions: 6,
            label: '${settingsProvider.voiceSpeed.toStringAsFixed(1)}x',
            onChanged: (double value) async {
              await settingsProvider.setVoiceSpeed(value);
              await _audioService.updateSettings(voiceSpeed: value);
            },
            activeColor: AppTheme.jungleGreen,
            inactiveColor: AppTheme.lightJungleGreen,
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageSetting(SettingsProvider settingsProvider) {
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: AppTheme.leafGreen,
          shape: BoxShape.circle,
        ),
        child: const Icon(
          Icons.language,
          color: AppTheme.white,
          size: 20,
        ),
      ),
      title: Text(
        'Language',
        style: AppTheme.titleStyle.copyWith(
          color: AppTheme.earthBrown,
          fontSize: 16,
        ),
      ),
      subtitle: Text(
        'App language (English only for now)',
        style: AppTheme.bodyStyle.copyWith(
          color: AppTheme.earthBrown.withValues(alpha: 0.7),
          fontSize: 14,
        ),
      ),
      trailing: Text(
        'English',
        style: AppTheme.bodyStyle.copyWith(
          color: AppTheme.earthBrown,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildInfoSetting({
    required String title,
    required String subtitle,
    required IconData icon,
  }) {
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: AppTheme.lightJungleGreen,
          shape: BoxShape.circle,
        ),
        child: Icon(
          icon,
          color: AppTheme.earthBrown,
          size: 20,
        ),
      ),
      title: Text(
        title,
        style: AppTheme.titleStyle.copyWith(
          color: AppTheme.earthBrown,
          fontSize: 16,
        ),
      ),
      trailing: Text(
        subtitle,
        style: AppTheme.bodyStyle.copyWith(
          color: AppTheme.earthBrown.withValues(alpha: 0.7),
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildActionSetting({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: AppTheme.earthBrown,
          shape: BoxShape.circle,
        ),
        child: Icon(
          icon,
          color: AppTheme.white,
          size: 20,
        ),
      ),
      title: Text(
        title,
        style: AppTheme.titleStyle.copyWith(
          color: AppTheme.earthBrown,
          fontSize: 16,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: AppTheme.bodyStyle.copyWith(
          color: AppTheme.earthBrown.withValues(alpha: 0.7),
          fontSize: 14,
        ),
      ),
      trailing: const Icon(
        Icons.arrow_forward_ios,
        color: AppTheme.earthBrown,
        size: 16,
      ),
      onTap: onTap,
    );
  }

  void _showResetDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Reset Progress',
            style: AppTheme.titleStyle.copyWith(color: AppTheme.earthBrown),
          ),
          content: Text(
            'Are you sure you want to reset all game progress? This action cannot be undone.',
            style: AppTheme.bodyStyle.copyWith(color: AppTheme.earthBrown),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: AppTheme.bodyStyle.copyWith(color: AppTheme.jungleGreen),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _resetProgress();
              },
              child: Text(
                'Reset',
                style: AppTheme.bodyStyle.copyWith(color: AppTheme.earthBrown),
              ),
            ),
          ],
        );
      },
    );
  }

  void _resetProgress() async {
    // Reset settings to defaults
    await context.read<SettingsProvider>().resetToDefaults();

    // Show confirmation
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Progress reset successfully!'),
          backgroundColor: AppTheme.leafGreen,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }
}
