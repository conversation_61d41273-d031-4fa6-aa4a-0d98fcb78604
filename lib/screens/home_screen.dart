import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../utils/app_theme.dart';
import '../utils/app_routes.dart';
import '../providers/settings_provider.dart';
import '../providers/rewards_provider.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late AnimationController _bounceController;

  @override
  void initState() {
    super.initState();
    _bounceController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _bounceController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _bounceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: context.watch<SettingsProvider>().isDarkMode
            ? AppTheme.nightJungleGradient
            : AppTheme.jungleGradient,
        child: Safe<PERSON><PERSON>(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              children: [
                // Header with title and stats
                _buildHeader(),
                
                const SizedBox(height: 40),
                
                // Animated monkey character
                _buildAnimatedMonkey(),
                
                const SizedBox(height: 40),
                
                // Menu buttons
                Expanded(
                  child: _buildMenuButtons(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Consumer<RewardsProvider>(
      builder: (context, rewardsProvider, child) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Monkey Math Magic',
                  style: AppTheme.headingStyle.copyWith(
                    color: AppTheme.white,
                    shadows: [
                      Shadow(
                        color: Colors.black.withOpacity(0.5),
                        blurRadius: 5,
                        offset: const Offset(2, 2),
                      ),
                    ],
                  ),
                ),
                Text(
                  'Ready for some fun?',
                  style: AppTheme.bodyStyle.copyWith(
                    color: AppTheme.white,
                    shadows: [
                      Shadow(
                        color: Colors.black.withOpacity(0.5),
                        blurRadius: 3,
                        offset: const Offset(1, 1),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            // Stats display
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppTheme.white.withOpacity(0.9),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      const Icon(Icons.star, color: AppTheme.bananaYellow, size: 20),
                      const SizedBox(width: 4),
                      Text(
                        '${rewardsProvider.bananas}',
                        style: AppTheme.bodyStyle.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.earthBrown,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(Icons.emoji_events, color: AppTheme.jungleGreen, size: 20),
                      const SizedBox(width: 4),
                      Text(
                        '${rewardsProvider.stars}',
                        style: AppTheme.bodyStyle.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.earthBrown,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildAnimatedMonkey() {
    return AnimatedBuilder(
      animation: _bounceController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _bounceController.value * 10 - 5),
          child: Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              color: AppTheme.monkeyBrown,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: const Icon(
              Icons.pets,
              size: 50,
              color: AppTheme.bananaYellow,
            ),
          ),
        );
      },
    );
  }

  Widget _buildMenuButtons() {
    return Column(
      children: [
        _buildMenuButton(
          title: 'Start Game',
          subtitle: 'Interactive multiplication',
          icon: Icons.play_arrow,
          color: AppTheme.jungleGreen,
          onTap: () => AppRoutes.navigateTo(context, AppRoutes.game),
        ).animate().slideX(begin: -1, delay: 100.ms),
        
        const SizedBox(height: 20),
        
        _buildMenuButton(
          title: 'Practice Mode',
          subtitle: 'Quiz yourself',
          icon: Icons.quiz,
          color: AppTheme.bananaYellow,
          textColor: AppTheme.earthBrown,
          onTap: () => AppRoutes.navigateTo(context, AppRoutes.practice),
        ).animate().slideX(begin: 1, delay: 200.ms),
        
        const SizedBox(height: 20),
        
        _buildMenuButton(
          title: 'My Rewards',
          subtitle: 'See your achievements',
          icon: Icons.emoji_events,
          color: AppTheme.earthBrown,
          onTap: () => AppRoutes.navigateTo(context, AppRoutes.rewards),
        ).animate().slideX(begin: -1, delay: 300.ms),
        
        const SizedBox(height: 20),
        
        _buildMenuButton(
          title: 'Settings',
          subtitle: 'Customize your experience',
          icon: Icons.settings,
          color: AppTheme.lightJungleGreen,
          textColor: AppTheme.earthBrown,
          onTap: () => AppRoutes.navigateTo(context, AppRoutes.settings),
        ).animate().slideX(begin: 1, delay: 400.ms),
      ],
    );
  }

  Widget _buildMenuButton({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    Color textColor = AppTheme.white,
    required VoidCallback onTap,
  }) {
    return Container(
      width: double.infinity,
      height: 80,
      child: ElevatedButton(
        onPressed: onTap,
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          foregroundColor: textColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          elevation: 8,
          shadowColor: Colors.black.withOpacity(0.3),
        ),
        child: Row(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: textColor.withOpacity(0.2),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: 30,
                color: textColor,
              ),
            ),
            const SizedBox(width: 20),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTheme.titleStyle.copyWith(
                      color: textColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: AppTheme.bodyStyle.copyWith(
                      color: textColor.withOpacity(0.8),
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: textColor.withOpacity(0.7),
              size: 20,
            ),
          ],
        ),
      ),
    );
  }
}
