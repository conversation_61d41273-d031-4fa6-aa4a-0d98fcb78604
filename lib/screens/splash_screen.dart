import 'package:flutter/material.dart';
import '../utils/app_theme.dart';
import '../utils/app_routes.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _swingController;
  late AnimationController _fadeController;

  @override
  void initState() {
    super.initState();
    
    _swingController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _startAnimations();
  }

  void _startAnimations() async {
    // Start swing animation
    _swingController.repeat(reverse: true);
    
    // Start fade in animation
    _fadeController.forward();
    
    // Navigate to home screen after 3 seconds
    await Future.delayed(const Duration(seconds: 3));
    if (mounted) {
      AppRoutes.navigateAndReplace(context, AppRoutes.home);
    }
  }

  @override
  void dispose() {
    _swingController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: AppTheme.jungleGradient,
        child: SafeArea(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Animated monkey swinging
                AnimatedBuilder(
                  animation: _swingController,
                  builder: (context, child) {
                    return Transform.rotate(
                      angle: _swingController.value * 0.3 - 0.15,
                      child: Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          color: AppTheme.monkeyBrown,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.3),
                              blurRadius: 10,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.pets,
                          size: 60,
                          color: AppTheme.bananaYellow,
                        ),
                      ),
                    );
                  },
                ),
                
                const SizedBox(height: 40),
                
                // App title with fade animation
                FadeTransition(
                  opacity: _fadeController,
                  child: Column(
                    children: [
                      Text(
                        'Monkey Math Magic',
                        style: AppTheme.headingStyle.copyWith(
                          fontSize: 36,
                          color: AppTheme.white,
                          shadows: [
                            Shadow(
                              color: Colors.black.withOpacity(0.5),
                              blurRadius: 5,
                              offset: const Offset(2, 2),
                            ),
                          ],
                        ),
                        textAlign: TextAlign.center,
                      ),
                      
                      const SizedBox(height: 16),
                      
                      Text(
                        'Learn multiplication through play!',
                        style: AppTheme.bodyStyle.copyWith(
                          fontSize: 18,
                          color: AppTheme.white,
                          shadows: [
                            Shadow(
                              color: Colors.black.withOpacity(0.5),
                              blurRadius: 3,
                              offset: const Offset(1, 1),
                            ),
                          ],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 60),
                
                // Loading indicator
                FadeTransition(
                  opacity: _fadeController,
                  child: const CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(AppTheme.bananaYellow),
                    strokeWidth: 3,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
