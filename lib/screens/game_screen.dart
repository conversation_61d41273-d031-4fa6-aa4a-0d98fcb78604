import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../utils/app_theme.dart';
import '../utils/app_routes.dart';
import '../providers/game_provider.dart';
import '../providers/settings_provider.dart';
import '../widgets/monkey_character.dart';
import '../widgets/number_button.dart';
import '../widgets/answer_display.dart';
import '../widgets/confetti_widget.dart';

class GameScreen extends StatefulWidget {
  const GameScreen({super.key});

  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen> with TickerProviderStateMixin {
  late AnimationController _celebrationController;
  late AnimationController _feedbackController;

  @override
  void initState() {
    super.initState();
    _celebrationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _feedbackController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Start the game when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<GameProvider>().startGame();
    });
  }

  @override
  void dispose() {
    _celebrationController.dispose();
    _feedbackController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: context.watch<SettingsProvider>().isDarkMode
            ? AppTheme.nightJungleGradient
            : AppTheme.jungleGradient,
        child: SafeArea(
          child: Stack(
            children: [
              // Main game content
              Column(
                children: [
                  // Header with score and back button
                  _buildHeader(),

                  // Answer display area
                  _buildAnswerArea(),

                  // Monkey character in the center
                  Expanded(
                    flex: 3,
                    child: _buildMonkeyArea(),
                  ),

                  // Number buttons at the bottom
                  _buildNumberButtons(),

                  // Control buttons
                  _buildControlButtons(),
                ],
              ),

              // Confetti overlay
              Consumer<GameProvider>(
                builder: (context, gameProvider, child) {
                  return CelebrationConfetti(
                    isPlaying: gameProvider.isAnswerCorrect &&
                               gameProvider.selectedNumbers.length == 2,
                  );
                },
              ),

              // Banana drop overlay
              Consumer<GameProvider>(
                builder: (context, gameProvider, child) {
                  return BananaDropWidget(
                    isPlaying: gameProvider.isAnswerCorrect &&
                               gameProvider.selectedNumbers.length == 2,
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Consumer<GameProvider>(
      builder: (context, gameProvider, child) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Back button
              IconButton(
                onPressed: () => AppRoutes.goBack(context),
                icon: const Icon(
                  Icons.arrow_back,
                  color: AppTheme.white,
                  size: 28,
                ),
              ),

              // Score display
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: AppTheme.white.withOpacity(0.9),
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.star, color: AppTheme.bananaYellow, size: 20),
                    const SizedBox(width: 4),
                    Text(
                      '${gameProvider.score}',
                      style: AppTheme.titleStyle.copyWith(
                        color: AppTheme.earthBrown,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(width: 16),
                    const Icon(Icons.trending_up, color: AppTheme.jungleGreen, size: 20),
                    const SizedBox(width: 4),
                    Text(
                      'L${gameProvider.level}',
                      style: AppTheme.titleStyle.copyWith(
                        color: AppTheme.earthBrown,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),

              // Pause button
              IconButton(
                onPressed: () => gameProvider.pauseGame(),
                icon: const Icon(
                  Icons.pause,
                  color: AppTheme.white,
                  size: 28,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAnswerArea() {
    return Consumer<GameProvider>(
      builder: (context, gameProvider, child) {
        return Container(
          height: 80,
          margin: const EdgeInsets.symmetric(horizontal: 20),
          child: AnswerDisplay(
            selectedNumbers: gameProvider.selectedNumbers,
            isCorrect: gameProvider.isAnswerCorrect,
            feedbackMessage: gameProvider.feedbackMessage,
          ),
        );
      },
    );
  }

  Widget _buildMonkeyArea() {
    return Consumer<GameProvider>(
      builder: (context, gameProvider, child) {
        return Center(
          child: MonkeyCharacter(
            selectedNumbers: gameProvider.selectedNumbers,
            isCorrect: gameProvider.isAnswerCorrect,
            onCelebration: () {
              _celebrationController.forward().then((_) {
                _celebrationController.reset();
              });
            },
            onNumberDropped: (number) {
              gameProvider.selectNumber(number);
            },
          ),
        );
      },
    );
  }

  Widget _buildNumberButtons() {
    return Consumer<GameProvider>(
      builder: (context, gameProvider, child) {
        return Container(
          height: 120,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 6,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
              childAspectRatio: 1,
            ),
            itemCount: 12,
            itemBuilder: (context, index) {
              final number = index + 1;
              final selectedCount = gameProvider.selectedNumbers.where((n) => n == number).length;
              final isSelected = selectedCount > 0;

              return NumberButton(
                number: number,
                isSelected: isSelected,
                selectedCount: selectedCount,
                onTap: () {
                  if (gameProvider.selectedNumbers.length < 2) {
                    gameProvider.selectNumber(number);
                  } else if (isSelected) {
                    gameProvider.removeSelectedNumber(number);
                  }
                },
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildControlButtons() {
    return Consumer<GameProvider>(
      builder: (context, gameProvider, child) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Clear button
              ElevatedButton.icon(
                onPressed: gameProvider.selectedNumbers.isNotEmpty
                    ? () => gameProvider.clearSelectedNumbers()
                    : null,
                icon: const Icon(Icons.clear),
                label: const Text('Clear'),
                style: AppTheme.secondaryButtonStyle,
              ),

              // Next problem button
              ElevatedButton.icon(
                onPressed: gameProvider.selectedNumbers.length == 2
                    ? () => gameProvider.nextProblem()
                    : null,
                icon: const Icon(Icons.arrow_forward),
                label: const Text('Next'),
                style: AppTheme.primaryButtonStyle,
              ),
            ],
          ),
        );
      },
    );
  }
}
