import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../utils/app_theme.dart';
import '../utils/app_routes.dart';
import '../providers/settings_provider.dart';
import '../models/multiplication_problem.dart';
import '../services/audio_service.dart';

enum PracticeMode {
  randomQuestions,
  specificTable,
  mixedReview,
}

class PracticeScreen extends StatefulWidget {
  const PracticeScreen({super.key});

  @override
  State<PracticeScreen> createState() => _PracticeScreenState();
}

class _PracticeScreenState extends State<PracticeScreen> with TickerProviderStateMixin {
  PracticeMode _selectedMode = PracticeMode.randomQuestions;
  int _selectedTable = 2;
  MultiplicationProblem? _currentProblem;
  List<int> _answerOptions = [];
  int? _selectedAnswer;
  bool _showResult = false;
  bool _isCorrect = false;
  int _score = 0;
  int _questionsAnswered = 0;
  int _correctAnswers = 0;
  late AnimationController _resultController;
  late AnimationController _progressController;
  final AudioService _audioService = AudioService();

  @override
  void initState() {
    super.initState();
    _resultController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _initializeAudio();
  }

  @override
  void dispose() {
    _resultController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  Future<void> _initializeAudio() async {
    await _audioService.initialize();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: context.watch<SettingsProvider>().isDarkMode
            ? AppTheme.nightJungleGradient
            : AppTheme.jungleGradient,
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              if (_currentProblem == null) ...[
                _buildModeSelection(),
                _buildStartButton(),
              ] else ...[
                _buildProgressBar(),
                _buildQuestionArea(),
                _buildAnswerOptions(),
                _buildControlButtons(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            onPressed: () => AppRoutes.goBack(context),
            icon: const Icon(
              Icons.arrow_back,
              color: AppTheme.white,
              size: 28,
            ),
          ),
          Text(
            'Practice Mode',
            style: AppTheme.headingStyle.copyWith(
              color: AppTheme.white,
              fontSize: 28,
            ),
          ),
          if (_currentProblem != null)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppTheme.white.withOpacity(0.9),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                'Score: $_score',
                style: AppTheme.bodyStyle.copyWith(
                  color: AppTheme.earthBrown,
                  fontWeight: FontWeight.bold,
                ),
              ),
            )
          else
            const SizedBox(width: 48),
        ],
      ),
    );
  }

  Widget _buildModeSelection() {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Choose Practice Mode',
              style: AppTheme.titleStyle.copyWith(
                color: AppTheme.white,
                fontSize: 24,
              ),
            ),

            const SizedBox(height: 40),

            _buildModeCard(
              mode: PracticeMode.randomQuestions,
              title: 'Random Questions',
              subtitle: 'Mixed multiplication problems',
              icon: Icons.shuffle,
            ).animate().slideX(begin: -1, delay: 100.ms),

            const SizedBox(height: 20),

            _buildModeCard(
              mode: PracticeMode.specificTable,
              title: 'Times Table',
              subtitle: 'Practice specific multiplication table',
              icon: Icons.grid_view,
            ).animate().slideX(begin: 1, delay: 200.ms),

            const SizedBox(height: 20),

            _buildModeCard(
              mode: PracticeMode.mixedReview,
              title: 'Mixed Review',
              subtitle: 'Review all learned tables',
              icon: Icons.quiz,
            ).animate().slideX(begin: -1, delay: 300.ms),

            if (_selectedMode == PracticeMode.specificTable) ...[
              const SizedBox(height: 30),
              _buildTableSelector(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildModeCard({
    required PracticeMode mode,
    required String title,
    required String subtitle,
    required IconData icon,
  }) {
    final isSelected = _selectedMode == mode;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedMode = mode;
        });
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.bananaYellow : AppTheme.white.withOpacity(0.9),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? AppTheme.earthBrown : AppTheme.jungleGreen,
            width: isSelected ? 3 : 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: isSelected ? 12 : 8,
              offset: Offset(0, isSelected ? 6 : 4),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: isSelected ? AppTheme.earthBrown : AppTheme.jungleGreen,
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: AppTheme.white,
                size: 30,
              ),
            ),
            const SizedBox(width: 20),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTheme.titleStyle.copyWith(
                      color: isSelected ? AppTheme.earthBrown : AppTheme.darkJungleGreen,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: AppTheme.bodyStyle.copyWith(
                      color: isSelected ? AppTheme.earthBrown.withOpacity(0.8) : AppTheme.darkJungleGreen.withOpacity(0.8),
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              const Icon(
                Icons.check_circle,
                color: AppTheme.earthBrown,
                size: 24,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTableSelector() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            'Select Times Table',
            style: AppTheme.titleStyle.copyWith(
              color: AppTheme.earthBrown,
            ),
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 12,
            runSpacing: 12,
            children: List.generate(12, (index) {
              final table = index + 1;
              final isSelected = _selectedTable == table;

              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedTable = table;
                  });
                },
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: isSelected ? AppTheme.jungleGreen : AppTheme.lightJungleGreen,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isSelected ? AppTheme.earthBrown : AppTheme.jungleGreen,
                      width: 2,
                    ),
                  ),
                  child: Center(
                    child: Text(
                      '$table',
                      style: AppTheme.numberStyle.copyWith(
                        color: isSelected ? AppTheme.white : AppTheme.earthBrown,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildStartButton() {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: SizedBox(
        width: double.infinity,
        height: 60,
        child: ElevatedButton.icon(
          onPressed: _startPractice,
          icon: const Icon(Icons.play_arrow, size: 28),
          label: Text(
            'Start Practice',
            style: AppTheme.buttonStyle.copyWith(fontSize: 22),
          ),
          style: AppTheme.primaryButtonStyle.copyWith(
            padding: WidgetStateProperty.all(
              const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProgressBar() {
    final progress = _questionsAnswered / 10; // 10 questions per session

    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Question ${_questionsAnswered + 1} of 10',
                style: AppTheme.bodyStyle.copyWith(
                  color: AppTheme.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                'Accuracy: ${_questionsAnswered > 0 ? ((_correctAnswers / _questionsAnswered) * 100).round() : 0}%',
                style: AppTheme.bodyStyle.copyWith(
                  color: AppTheme.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: AppTheme.white.withValues(alpha: 0.3),
            valueColor: const AlwaysStoppedAnimation<Color>(AppTheme.bananaYellow),
            minHeight: 8,
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionArea() {
    if (_currentProblem == null) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppTheme.white.withValues(alpha: 0.95),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            'What is',
            style: AppTheme.bodyStyle.copyWith(
              color: AppTheme.earthBrown,
              fontSize: 18,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '${_currentProblem!.firstNumber}',
                style: AppTheme.headingStyle.copyWith(
                  color: AppTheme.jungleGreen,
                  fontSize: 48,
                ),
              ),
              const SizedBox(width: 20),
              Text(
                '×',
                style: AppTheme.headingStyle.copyWith(
                  color: AppTheme.earthBrown,
                  fontSize: 48,
                ),
              ),
              const SizedBox(width: 20),
              Text(
                '${_currentProblem!.secondNumber}',
                style: AppTheme.headingStyle.copyWith(
                  color: AppTheme.jungleGreen,
                  fontSize: 48,
                ),
              ),
              const SizedBox(width: 20),
              Text(
                '?',
                style: AppTheme.headingStyle.copyWith(
                  color: AppTheme.earthBrown,
                  fontSize: 48,
                ),
              ),
            ],
          ),
          if (_showResult) ...[
            const SizedBox(height: 20),
            AnimatedBuilder(
              animation: _resultController,
              builder: (context, child) {
                return Transform.scale(
                  scale: 1.0 + (_resultController.value * 0.2),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: _isCorrect ? AppTheme.leafGreen : AppTheme.bananaYellow,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          _isCorrect ? Icons.check_circle : Icons.lightbulb,
                          color: _isCorrect ? AppTheme.white : AppTheme.earthBrown,
                          size: 24,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _isCorrect ? 'Correct!' : 'Try again!',
                          style: AppTheme.titleStyle.copyWith(
                            color: _isCorrect ? AppTheme.white : AppTheme.earthBrown,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAnswerOptions() {
    if (_answerOptions.isEmpty) return const SizedBox.shrink();

    return Expanded(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: GridView.builder(
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 2,
          ),
          itemCount: _answerOptions.length,
          itemBuilder: (context, index) {
            final option = _answerOptions[index];
            final isSelected = _selectedAnswer == option;
            final isCorrect = option == _currentProblem!.answer;
            final showResult = _showResult && (isSelected || isCorrect);

            return GestureDetector(
              onTap: _showResult ? null : () => _selectAnswer(option),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                decoration: BoxDecoration(
                  color: showResult
                      ? (isCorrect ? AppTheme.leafGreen : AppTheme.bananaYellow)
                      : (isSelected ? AppTheme.bananaYellow : AppTheme.white),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: showResult
                        ? (isCorrect ? AppTheme.darkJungleGreen : AppTheme.earthBrown)
                        : (isSelected ? AppTheme.earthBrown : AppTheme.jungleGreen),
                    width: showResult ? 3 : 2,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.2),
                      blurRadius: isSelected ? 8 : 4,
                      offset: Offset(0, isSelected ? 4 : 2),
                    ),
                  ],
                ),
                child: Center(
                  child: Text(
                    '$option',
                    style: AppTheme.headingStyle.copyWith(
                      color: showResult
                          ? (isCorrect ? AppTheme.white : AppTheme.earthBrown)
                          : (isSelected ? AppTheme.earthBrown : AppTheme.darkJungleGreen),
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildControlButtons() {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _questionsAnswered >= 10 ? _finishPractice : null,
              icon: const Icon(Icons.stop),
              label: const Text('Finish'),
              style: AppTheme.secondaryButtonStyle,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _showResult ? _nextQuestion : null,
              icon: const Icon(Icons.arrow_forward),
              label: const Text('Next'),
              style: AppTheme.primaryButtonStyle,
            ),
          ),
        ],
      ),
    );
  }

  void _startPractice() {
    setState(() {
      _score = 0;
      _questionsAnswered = 0;
      _correctAnswers = 0;
    });
    _generateQuestion();
  }

  void _generateQuestion() {
    setState(() {
      _showResult = false;
      _selectedAnswer = null;

      switch (_selectedMode) {
        case PracticeMode.randomQuestions:
          _currentProblem = MultiplicationProblem.random(12);
          break;
        case PracticeMode.specificTable:
          _currentProblem = MultiplicationProblem.forTable(_selectedTable, 12);
          break;
        case PracticeMode.mixedReview:
          _currentProblem = MultiplicationProblem.random(10);
          break;
      }

      _answerOptions = _currentProblem!.getMultipleChoiceOptions();
    });
  }

  void _selectAnswer(int answer) async {
    setState(() {
      _selectedAnswer = answer;
      _showResult = true;
      _isCorrect = answer == _currentProblem!.answer;

      if (_isCorrect) {
        _correctAnswers++;
        _score += 10;
      }

      _questionsAnswered++;
    });

    // Play audio feedback
    if (_isCorrect) {
      await _audioService.playCorrectSound();
      await _audioService.speakMultiplication(
        _currentProblem!.firstNumber,
        _currentProblem!.secondNumber,
        _currentProblem!.answer,
      );
    } else {
      await _audioService.playIncorrectSound();
      await _audioService.speakEncouragement('Try again!');
    }

    // Animate result
    _resultController.forward().then((_) {
      _resultController.reverse();
    });
  }

  void _nextQuestion() {
    if (_questionsAnswered >= 10) {
      _finishPractice();
    } else {
      _generateQuestion();
    }
  }

  void _finishPractice() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Practice Complete!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Score: $_score'),
            Text('Accuracy: ${((_correctAnswers / _questionsAnswered) * 100).round()}%'),
            Text('Correct: $_correctAnswers / $_questionsAnswered'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                _currentProblem = null;
              });
            },
            child: const Text('Practice Again'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              AppRoutes.goBack(context);
            },
            child: const Text('Back to Home'),
          ),
        ],
      ),
    );
  }
}
