import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../utils/app_theme.dart';

class AnswerDisplay extends StatefulWidget {
  final List<int> selectedNumbers;
  final bool isCorrect;
  final String feedbackMessage;

  const AnswerDisplay({
    super.key,
    required this.selectedNumbers,
    required this.isCorrect,
    required this.feedbackMessage,
  });

  @override
  State<AnswerDisplay> createState() => _AnswerDisplayState();
}

class _AnswerDisplayState extends State<AnswerDisplay>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _feedbackController;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _feedbackController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _feedbackController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(AnswerDisplay oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Trigger animations when answer is provided
    if (widget.selectedNumbers.length == 2 && 
        oldWidget.selectedNumbers.length != 2) {
      _pulseController.forward().then((_) {
        _pulseController.reverse();
      });
    }
    
    // Trigger feedback animation when feedback changes
    if (widget.feedbackMessage != oldWidget.feedbackMessage &&
        widget.feedbackMessage.isNotEmpty) {
      _feedbackController.forward().then((_) {
        _feedbackController.reverse();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        color: AppTheme.white.withOpacity(0.95),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Equation display
          _buildEquation(),
          
          const SizedBox(height: 8),
          
          // Feedback message
          if (widget.feedbackMessage.isNotEmpty)
            _buildFeedback(),
        ],
      ),
    );
  }

  Widget _buildEquation() {
    return AnimatedBuilder(
      animation: _pulseController,
      builder: (context, child) {
        final scale = 1.0 + (_pulseController.value * 0.1);
        
        return Transform.scale(
          scale: scale,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // First number
              _buildNumberSlot(0),
              
              const SizedBox(width: 16),
              
              // Multiplication symbol
              Text(
                '×',
                style: AppTheme.headingStyle.copyWith(
                  fontSize: 32,
                  color: AppTheme.earthBrown,
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Second number
              _buildNumberSlot(1),
              
              const SizedBox(width: 16),
              
              // Equals symbol
              Text(
                '=',
                style: AppTheme.headingStyle.copyWith(
                  fontSize: 32,
                  color: AppTheme.earthBrown,
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Result
              _buildResult(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildNumberSlot(int index) {
    final hasNumber = widget.selectedNumbers.length > index;
    final number = hasNumber ? widget.selectedNumbers[index] : null;
    
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        color: hasNumber ? AppTheme.bananaYellow : AppTheme.lightJungleGreen,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.jungleGreen,
          width: 2,
        ),
      ),
      child: Center(
        child: Text(
          number?.toString() ?? '?',
          style: AppTheme.numberStyle.copyWith(
            fontSize: 24,
            color: hasNumber ? AppTheme.earthBrown : AppTheme.darkJungleGreen,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildResult() {
    final hasResult = widget.selectedNumbers.length == 2;
    final result = hasResult 
        ? widget.selectedNumbers[0] * widget.selectedNumbers[1] 
        : null;
    
    return Container(
      width: 60,
      height: 50,
      decoration: BoxDecoration(
        color: hasResult 
            ? (widget.isCorrect ? AppTheme.leafGreen : AppTheme.lightEarthBrown)
            : AppTheme.lightJungleGreen,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: hasResult 
              ? (widget.isCorrect ? AppTheme.darkJungleGreen : AppTheme.earthBrown)
              : AppTheme.jungleGreen,
          width: 2,
        ),
      ),
      child: Center(
        child: Text(
          result?.toString() ?? '?',
          style: AppTheme.numberStyle.copyWith(
            fontSize: 20,
            color: hasResult 
                ? (widget.isCorrect ? AppTheme.white : AppTheme.earthBrown)
                : AppTheme.darkJungleGreen,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildFeedback() {
    return AnimatedBuilder(
      animation: _feedbackController,
      builder: (context, child) {
        final scale = 1.0 + (_feedbackController.value * 0.2);
        
        return Transform.scale(
          scale: scale,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: widget.isCorrect 
                  ? AppTheme.leafGreen 
                  : AppTheme.bananaYellow,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  widget.isCorrect ? Icons.check_circle : Icons.lightbulb,
                  color: widget.isCorrect ? AppTheme.white : AppTheme.earthBrown,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  widget.feedbackMessage,
                  style: AppTheme.bodyStyle.copyWith(
                    color: widget.isCorrect ? AppTheme.white : AppTheme.earthBrown,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
