import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../utils/app_theme.dart';

class NumberButton extends StatefulWidget {
  final int number;
  final bool isSelected;
  final int selectedCount;
  final VoidCallback onTap;

  const NumberButton({
    super.key,
    required this.number,
    required this.isSelected,
    this.selectedCount = 0,
    required this.onTap,
  });

  @override
  State<NumberButton> createState() => _NumberButtonState();
}

class _NumberButtonState extends State<NumberButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTap() {
    _animationController.forward().then((_) {
      _animationController.reverse();
    });
    widget.onTap();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Draggable<int>(
            data: widget.number,
            feedback: _buildDragFeedback(),
            childWhenDragging: _buildChildWhenDragging(),
            onDragStarted: () {
              _animationController.forward();
            },
            onDragEnd: (details) {
              _animationController.reverse();
            },
            child: GestureDetector(
              onTap: _handleTap,
              child: _buildButton(),
            ),
          ),
        );
      },
    );
  }

  Widget _buildButton() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      decoration: BoxDecoration(
        color: widget.isSelected
            ? AppTheme.bananaYellow
            : AppTheme.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: widget.isSelected
              ? AppTheme.earthBrown
              : AppTheme.jungleGreen,
          width: widget.isSelected ? 3 : 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: widget.isSelected ? 8 : 4,
            offset: Offset(0, widget.isSelected ? 4 : 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          Center(
            child: Text(
              '${widget.number}',
              style: AppTheme.numberStyle.copyWith(
                color: widget.isSelected
                    ? AppTheme.earthBrown
                    : AppTheme.darkJungleGreen,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          // Show count indicator when selected multiple times
          if (widget.selectedCount > 1)
            Positioned(
              top: 4,
              right: 4,
              child: Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  color: AppTheme.earthBrown,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    '${widget.selectedCount}',
                    style: AppTheme.bodyStyle.copyWith(
                      color: AppTheme.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDragFeedback() {
    return Material(
      color: Colors.transparent,
      child: Transform.scale(
        scale: 1.2,
        child: Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: AppTheme.bananaYellow,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: AppTheme.earthBrown,
              width: 3,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.4),
                blurRadius: 12,
                offset: const Offset(0, 6),
              ),
            ],
          ),
          child: Center(
            child: Text(
              '${widget.number}',
              style: AppTheme.numberStyle.copyWith(
                color: AppTheme.earthBrown,
                fontSize: 28,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildChildWhenDragging() {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.lightJungleGreen.withOpacity(0.5),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.jungleGreen.withOpacity(0.5),
          width: 2,
        ),
      ),
      child: Center(
        child: Text(
          '${widget.number}',
          style: AppTheme.numberStyle.copyWith(
            color: AppTheme.darkJungleGreen.withOpacity(0.5),
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}
