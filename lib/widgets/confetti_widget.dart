import 'package:flutter/material.dart';
import 'package:confetti/confetti.dart';
import '../utils/app_theme.dart';

class CelebrationConfetti extends StatefulWidget {
  final bool isPlaying;
  final VoidCallback? onAnimationComplete;

  const CelebrationConfetti({
    super.key,
    required this.isPlaying,
    this.onAnimationComplete,
  });

  @override
  State<CelebrationConfetti> createState() => _CelebrationConfettiState();
}

class _CelebrationConfettiState extends State<CelebrationConfetti> {
  late ConfettiController _confettiController;

  @override
  void initState() {
    super.initState();
    _confettiController = ConfettiController(duration: const Duration(seconds: 2));
  }

  @override
  void dispose() {
    _confettiController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(CelebrationConfetti oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isPlaying && !oldWidget.isPlaying) {
      _confettiController.play();
      
      // Call completion callback after animation
      Future.delayed(const Duration(seconds: 2), () {
        if (widget.onAnimationComplete != null) {
          widget.onAnimationComplete!();
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Left confetti
        Positioned(
          top: 0,
          left: 0,
          child: ConfettiWidget(
            confettiController: _confettiController,
            blastDirection: 0, // radians - 0 is to the right
            particleDrag: 0.05,
            emissionFrequency: 0.05,
            numberOfParticles: 20,
            gravity: 0.1,
            shouldLoop: false,
            colors: const [
              AppTheme.bananaYellow,
              AppTheme.leafGreen,
              AppTheme.jungleGreen,
              AppTheme.earthBrown,
              AppTheme.lightJungleGreen,
            ],
          ),
        ),

        // Right confetti
        Positioned(
          top: 0,
          right: 0,
          child: ConfettiWidget(
            confettiController: _confettiController,
            blastDirection: 3.14, // radians - pi is to the left
            particleDrag: 0.05,
            emissionFrequency: 0.05,
            numberOfParticles: 20,
            gravity: 0.1,
            shouldLoop: false,
            colors: const [
              AppTheme.bananaYellow,
              AppTheme.leafGreen,
              AppTheme.jungleGreen,
              AppTheme.earthBrown,
              AppTheme.lightJungleGreen,
            ],
          ),
        ),

        // Center confetti (falling down)
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: ConfettiWidget(
            confettiController: _confettiController,
            blastDirection: 1.57, // radians - pi/2 is down
            particleDrag: 0.05,
            emissionFrequency: 0.05,
            numberOfParticles: 30,
            gravity: 0.2,
            shouldLoop: false,
            colors: const [
              AppTheme.bananaYellow,
              AppTheme.leafGreen,
              AppTheme.jungleGreen,
              AppTheme.earthBrown,
              AppTheme.lightJungleGreen,
            ],
          ),
        ),
      ],
    );
  }
}

// Banana drop animation widget
class BananaDropWidget extends StatefulWidget {
  final bool isPlaying;
  final VoidCallback? onAnimationComplete;

  const BananaDropWidget({
    super.key,
    required this.isPlaying,
    this.onAnimationComplete,
  });

  @override
  State<BananaDropWidget> createState() => _BananaDropWidgetState();
}

class _BananaDropWidgetState extends State<BananaDropWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fallAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _fallAnimation = Tween<double>(
      begin: -100,
      end: 600,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.bounceOut,
    ));
    
    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 6.28, // 2 * pi for full rotation
    ).animate(_animationController);
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.5,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(BananaDropWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isPlaying && !oldWidget.isPlaying) {
      _animationController.forward().then((_) {
        _animationController.reset();
        if (widget.onAnimationComplete != null) {
          widget.onAnimationComplete!();
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Positioned(
          top: _fallAnimation.value,
          left: MediaQuery.of(context).size.width * 0.1,
          right: MediaQuery.of(context).size.width * 0.1,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: List.generate(5, (index) {
              return Transform.rotate(
                angle: _rotationAnimation.value + (index * 0.5),
                child: Transform.scale(
                  scale: _scaleAnimation.value,
                  child: Container(
                    width: 30,
                    height: 40,
                    decoration: BoxDecoration(
                      color: AppTheme.bananaYellow,
                      borderRadius: BorderRadius.circular(15),
                      border: Border.all(
                        color: AppTheme.earthBrown,
                        width: 2,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.3),
                          blurRadius: 5,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: const Center(
                      child: Text(
                        '🍌',
                        style: TextStyle(fontSize: 20),
                      ),
                    ),
                  ),
                ),
              );
            }),
          ),
        );
      },
    );
  }
}
