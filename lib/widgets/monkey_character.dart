import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../utils/app_theme.dart';

class MonkeyCharacter extends StatefulWidget {
  final List<int> selectedNumbers;
  final bool isCorrect;
  final VoidCallback onCelebration;
  final Function(int) onNumberDropped;

  const MonkeyCharacter({
    super.key,
    required this.selectedNumbers,
    required this.isCorrect,
    required this.onCelebration,
    required this.onNumberDropped,
  });

  @override
  State<MonkeyCharacter> createState() => _MonkeyCharacterState();
}

class _MonkeyCharacterState extends State<MonkeyCharacter>
    with TickerProviderStateMixin {
  late AnimationController _idleController;
  late AnimationController _celebrationController;
  late AnimationController _feetController;
  late AnimationController _handsController;

  @override
  void initState() {
    super.initState();
    
    _idleController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );
    
    _celebrationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _feetController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _handsController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    // Start idle animation
    _idleController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _idleController.dispose();
    _celebrationController.dispose();
    _feetController.dispose();
    _handsController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(MonkeyCharacter oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Animate feet when numbers are selected
    if (widget.selectedNumbers.length != oldWidget.selectedNumbers.length) {
      _feetController.forward().then((_) {
        _feetController.reverse();
      });
    }
    
    // Animate hands when answer is complete
    if (widget.selectedNumbers.length == 2 && 
        oldWidget.selectedNumbers.length != 2) {
      _handsController.forward();
    }
    
    // Celebration animation for correct answers
    if (widget.isCorrect && !oldWidget.isCorrect) {
      _celebrationController.forward().then((_) {
        _celebrationController.reverse();
        widget.onCelebration();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        width: 250,
        height: 300,
        child: Stack(
          alignment: Alignment.center,
          children: [
            // Monkey body
            _buildMonkeyBody(),
            
            // Left foot
            Positioned(
              bottom: 20,
              left: 60,
              child: _buildFoot(0),
            ),
            
            // Right foot
            Positioned(
              bottom: 20,
              right: 60,
              child: _buildFoot(1),
            ),
            
            // Left hand
            Positioned(
              top: 80,
              left: 40,
              child: _buildHand(true),
            ),
            
            // Right hand
            Positioned(
              top: 80,
              right: 40,
              child: _buildHand(false),
            ),
            
            // Answer display above hands
            if (widget.selectedNumbers.length == 2)
              Positioned(
                top: 30,
                child: _buildAnswerBubble(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildMonkeyBody() {
    return AnimatedBuilder(
      animation: Listenable.merge([_idleController, _celebrationController]),
      builder: (context, child) {
        final idleOffset = _idleController.value * 10 - 5;
        final celebrationScale = 1.0 + (_celebrationController.value * 0.2);
        final celebrationRotation = _celebrationController.value * 0.1 - 0.05;
        
        return Transform.translate(
          offset: Offset(0, idleOffset),
          child: Transform.scale(
            scale: celebrationScale,
            child: Transform.rotate(
              angle: celebrationRotation,
              child: Container(
                width: 120,
                height: 150,
                decoration: BoxDecoration(
                  color: AppTheme.monkeyBrown,
                  borderRadius: BorderRadius.circular(60),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 15,
                      offset: const Offset(0, 8),
                    ),
                  ],
                ),
                child: Stack(
                  children: [
                    // Face
                    Positioned(
                      top: 20,
                      left: 20,
                      right: 20,
                      child: Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: AppTheme.lightEarthBrown,
                          shape: BoxShape.circle,
                        ),
                        child: _buildFace(),
                      ),
                    ),
                    
                    // Belly
                    Positioned(
                      bottom: 20,
                      left: 30,
                      right: 30,
                      child: Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          color: AppTheme.lightEarthBrown,
                          borderRadius: BorderRadius.circular(30),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildFace() {
    return Stack(
      children: [
        // Eyes
        Positioned(
          top: 20,
          left: 15,
          child: Container(
            width: 12,
            height: 12,
            decoration: const BoxDecoration(
              color: Colors.black,
              shape: BoxShape.circle,
            ),
          ),
        ),
        Positioned(
          top: 20,
          right: 15,
          child: Container(
            width: 12,
            height: 12,
            decoration: const BoxDecoration(
              color: Colors.black,
              shape: BoxShape.circle,
            ),
          ),
        ),
        
        // Nose
        Positioned(
          top: 35,
          left: 35,
          child: Container(
            width: 8,
            height: 6,
            decoration: BoxDecoration(
              color: Colors.black,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ),
        
        // Mouth
        Positioned(
          top: 45,
          left: 25,
          child: Container(
            width: 30,
            height: 15,
            decoration: BoxDecoration(
              color: widget.isCorrect ? AppTheme.leafGreen : Colors.black,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(15),
                bottomRight: Radius.circular(15),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFoot(int index) {
    final hasNumber = widget.selectedNumbers.length > index;
    final number = hasNumber ? widget.selectedNumbers[index] : null;

    return AnimatedBuilder(
      animation: _feetController,
      builder: (context, child) {
        final bounce = _feetController.value * 10;

        return Transform.translate(
          offset: Offset(0, -bounce),
          child: DragTarget<int>(
            onWillAcceptWithDetails: (details) {
              // Only accept if we have space for more numbers
              return widget.selectedNumbers.length < 2;
            },
            onAcceptWithDetails: (details) {
              // This will be handled by the parent widget through callbacks
              widget.onNumberDropped(details.data);
            },
            builder: (context, candidateData, rejectedData) {
              final isHighlighted = candidateData.isNotEmpty;

              return AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: isHighlighted
                      ? AppTheme.bananaYellow
                      : AppTheme.earthBrown,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: hasNumber
                        ? AppTheme.bananaYellow
                        : (isHighlighted ? AppTheme.leafGreen : AppTheme.darkJungleGreen),
                    width: isHighlighted ? 4 : 3,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: isHighlighted ? 12 : 8,
                      offset: Offset(0, isHighlighted ? 6 : 4),
                    ),
                  ],
                ),
                child: Center(
                  child: Text(
                    number?.toString() ?? (isHighlighted ? '?' : ''),
                    style: AppTheme.numberStyle.copyWith(
                      color: isHighlighted
                          ? AppTheme.earthBrown
                          : AppTheme.white,
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }



  Widget _buildHand(bool isLeft) {
    return AnimatedBuilder(
      animation: _handsController,
      builder: (context, child) {
        final pointingAngle = _handsController.value * (isLeft ? -0.5 : 0.5);
        
        return Transform.rotate(
          angle: pointingAngle,
          child: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppTheme.monkeyBrown,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: const Icon(
              Icons.pan_tool,
              color: AppTheme.lightEarthBrown,
              size: 24,
            ),
          ),
        );
      },
    );
  }

  Widget _buildAnswerBubble() {
    final result = widget.selectedNumbers[0] * widget.selectedNumbers[1];
    
    return AnimatedBuilder(
      animation: _handsController,
      builder: (context, child) {
        final scale = _handsController.value;
        
        return Transform.scale(
          scale: scale,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: widget.isCorrect ? AppTheme.leafGreen : AppTheme.bananaYellow,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Text(
              result.toString(),
              style: AppTheme.headingStyle.copyWith(
                color: widget.isCorrect ? AppTheme.white : AppTheme.earthBrown,
                fontSize: 28,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        );
      },
    );
  }
}
