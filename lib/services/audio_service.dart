import 'package:flutter/foundation.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:audioplayers/audioplayers.dart';

class AudioService {
  static final AudioService _instance = AudioService._internal();
  factory AudioService() => _instance;
  AudioService._internal();

  late FlutterTts _flutterTts;
  late AudioPlayer _audioPlayer;
  bool _isInitialized = false;
  bool _soundEnabled = true;
  String _voiceGender = 'female';
  double _voiceSpeed = 1.0;

  // Initialize the audio service
  Future<void> initialize() async {
    if (_isInitialized) return;

    _flutterTts = FlutterTts();
    _audioPlayer = AudioPlayer();

    // Configure TTS
    await _configureTts();
    
    _isInitialized = true;
  }

  Future<void> _configureTts() async {
    try {
      // Set language
      await _flutterTts.setLanguage('en-US');
      
      // Set speech rate
      await _flutterTts.setSpeechRate(_voiceSpeed);
      
      // Set volume
      await _flutterTts.setVolume(1.0);
      
      // Set pitch
      await _flutterTts.setPitch(1.0);
      
      // Configure voice based on gender preference
      if (!kIsWeb) {
        final voices = await _flutterTts.getVoices;
        if (voices != null) {
          final preferredVoices = voices.where((voice) {
            final name = voice['name'].toString().toLowerCase();
            return _voiceGender == 'female' 
                ? name.contains('female') || name.contains('woman') || name.contains('samantha')
                : name.contains('male') || name.contains('man') || name.contains('alex');
          }).toList();
          
          if (preferredVoices.isNotEmpty) {
            await _flutterTts.setVoice({
              'name': preferredVoices.first['name'],
              'locale': preferredVoices.first['locale'],
            });
          }
        }
      }
    } catch (e) {
      debugPrint('Error configuring TTS: $e');
    }
  }

  // Update settings
  Future<void> updateSettings({
    bool? soundEnabled,
    String? voiceGender,
    double? voiceSpeed,
  }) async {
    if (soundEnabled != null) _soundEnabled = soundEnabled;
    if (voiceGender != null) _voiceGender = voiceGender;
    if (voiceSpeed != null) _voiceSpeed = voiceSpeed;
    
    if (_isInitialized) {
      await _configureTts();
    }
  }

  // Speak multiplication problem
  Future<void> speakMultiplication(int firstNumber, int secondNumber, int result) async {
    if (!_soundEnabled || !_isInitialized) return;
    
    final text = '$firstNumber times $secondNumber equals $result';
    await _speak(text);
  }

  // Speak encouragement
  Future<void> speakEncouragement(String message) async {
    if (!_soundEnabled || !_isInitialized) return;
    
    await _speak(message);
  }

  // Speak level up message
  Future<void> speakLevelUp(int level) async {
    if (!_soundEnabled || !_isInitialized) return;
    
    await _speak('Congratulations! You reached level $level!');
  }

  // Generic speak method
  Future<void> _speak(String text) async {
    try {
      await _flutterTts.stop();
      await _flutterTts.speak(text);
    } catch (e) {
      debugPrint('Error speaking: $e');
    }
  }

  // Play sound effects
  Future<void> playCorrectSound() async {
    if (!_soundEnabled || !_isInitialized) return;
    
    try {
      // For now, we'll use a simple beep sound
      // In a real app, you'd load actual sound files
      await _playBeep(frequency: 800, duration: 200);
    } catch (e) {
      debugPrint('Error playing correct sound: $e');
    }
  }

  Future<void> playIncorrectSound() async {
    if (!_soundEnabled || !_isInitialized) return;
    
    try {
      await _playBeep(frequency: 400, duration: 300);
    } catch (e) {
      debugPrint('Error playing incorrect sound: $e');
    }
  }

  Future<void> playButtonTapSound() async {
    if (!_soundEnabled || !_isInitialized) return;
    
    try {
      await _playBeep(frequency: 600, duration: 100);
    } catch (e) {
      debugPrint('Error playing button tap sound: $e');
    }
  }

  Future<void> playCelebrationSound() async {
    if (!_soundEnabled || !_isInitialized) return;
    
    try {
      // Play a series of ascending beeps for celebration
      for (int i = 0; i < 3; i++) {
        await _playBeep(frequency: 600 + (i * 200), duration: 150);
        await Future.delayed(const Duration(milliseconds: 100));
      }
    } catch (e) {
      debugPrint('Error playing celebration sound: $e');
    }
  }

  // Simple beep generator (for web compatibility)
  Future<void> _playBeep({required double frequency, required int duration}) async {
    // This is a simplified implementation
    // In a real app, you'd use actual audio files or generate proper tones
    if (kIsWeb) {
      // For web, we'll skip the beep sounds for now
      return;
    }
    
    // For mobile platforms, you could implement tone generation
    // or use pre-recorded sound files
  }

  // Play jungle ambient sounds
  Future<void> playJungleAmbient() async {
    if (!_soundEnabled || !_isInitialized) return;
    
    try {
      // In a real app, you'd load jungle ambient sound files
      // await _audioPlayer.play(AssetSource('sounds/jungle_ambient.mp3'));
    } catch (e) {
      debugPrint('Error playing jungle ambient: $e');
    }
  }

  // Stop all sounds
  Future<void> stopAllSounds() async {
    if (!_isInitialized) return;
    
    try {
      await _flutterTts.stop();
      await _audioPlayer.stop();
    } catch (e) {
      debugPrint('Error stopping sounds: $e');
    }
  }

  // Dispose resources
  Future<void> dispose() async {
    if (!_isInitialized) return;
    
    await stopAllSounds();
    await _audioPlayer.dispose();
    _isInitialized = false;
  }

  // Get available voices (for settings)
  Future<List<Map<String, String>>> getAvailableVoices() async {
    if (!_isInitialized) return [];
    
    try {
      final voices = await _flutterTts.getVoices;
      if (voices != null) {
        return voices.map<Map<String, String>>((voice) => {
          'name': voice['name'].toString(),
          'locale': voice['locale'].toString(),
        }).toList();
      }
    } catch (e) {
      debugPrint('Error getting voices: $e');
    }
    
    return [];
  }

  // Check if TTS is available
  Future<bool> isTtsAvailable() async {
    try {
      final languages = await _flutterTts.getLanguages;
      return languages != null && languages.isNotEmpty;
    } catch (e) {
      debugPrint('Error checking TTS availability: $e');
      return false;
    }
  }
}
