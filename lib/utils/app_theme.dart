import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  // Jungle Color Palette
  static const Color jungleGreen = Color(0xFF228B22);
  static const Color darkJungleGreen = Color(0xFF006400);
  static const Color lightJungleGreen = Color(0xFF90EE90);
  static const Color bananaYellow = Color(0xFFFFE135);
  static const Color darkBananaYellow = Color(0xFFFFD700);
  static const Color earthBrown = Color(0xFF8B4513);
  static const Color lightEarthBrown = Color(0xFFD2B48C);
  static const Color monkeyBrown = Color(0xFF8B4513);
  static const Color leafGreen = Color(0xFF32CD32);
  static const Color skyBlue = Color(0xFF87CEEB);
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);

  // Text Styles
  static TextStyle get headingStyle => GoogleFonts.baloo2(
        fontSize: 32,
        fontWeight: FontWeight.bold,
        color: earthBrown,
      );

  static TextStyle get titleStyle => GoogleFonts.baloo2(
        fontSize: 24,
        fontWeight: FontWeight.w600,
        color: darkJungleGreen,
      );

  static TextStyle get bodyStyle => GoogleFonts.baloo2(
        fontSize: 18,
        fontWeight: FontWeight.normal,
        color: earthBrown,
      );

  static TextStyle get buttonStyle => GoogleFonts.baloo2(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: white,
      );

  static TextStyle get numberStyle => GoogleFonts.baloo2(
        fontSize: 28,
        fontWeight: FontWeight.bold,
        color: darkJungleGreen,
      );

  // Light Theme
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: jungleGreen,
        brightness: Brightness.light,
        primary: jungleGreen,
        secondary: bananaYellow,
        surface: lightJungleGreen,
        background: skyBlue,
      ),
      textTheme: TextTheme(
        headlineLarge: headingStyle,
        headlineMedium: titleStyle,
        bodyLarge: bodyStyle,
        labelLarge: buttonStyle,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: jungleGreen,
          foregroundColor: white,
          textStyle: buttonStyle,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        ),
      ),
      cardTheme: CardThemeData(
        color: white,
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: jungleGreen,
        foregroundColor: white,
        titleTextStyle: titleStyle.copyWith(color: white),
        elevation: 0,
      ),
    );
  }

  // Dark Theme (Night Jungle)
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: darkJungleGreen,
        brightness: Brightness.dark,
        primary: darkJungleGreen,
        secondary: darkBananaYellow,
        surface: earthBrown,
        background: const Color(0xFF1A1A1A),
      ),
      textTheme: TextTheme(
        headlineLarge: headingStyle.copyWith(color: bananaYellow),
        headlineMedium: titleStyle.copyWith(color: lightJungleGreen),
        bodyLarge: bodyStyle.copyWith(color: lightEarthBrown),
        labelLarge: buttonStyle,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: darkJungleGreen,
          foregroundColor: bananaYellow,
          textStyle: buttonStyle.copyWith(color: bananaYellow),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        ),
      ),
      cardTheme: CardThemeData(
        color: earthBrown,
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: darkJungleGreen,
        foregroundColor: bananaYellow,
        titleTextStyle: titleStyle.copyWith(color: bananaYellow),
        elevation: 0,
      ),
    );
  }

  // Custom Decorations
  static BoxDecoration get jungleGradient => BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            skyBlue,
            lightJungleGreen,
            jungleGreen,
          ],
        ),
      );

  static BoxDecoration get nightJungleGradient => BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            const Color(0xFF1A1A1A),
            darkJungleGreen,
            earthBrown,
          ],
        ),
      );

  // Button Styles
  static ButtonStyle get primaryButtonStyle => ElevatedButton.styleFrom(
        backgroundColor: jungleGreen,
        foregroundColor: white,
        textStyle: buttonStyle,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
        elevation: 8,
      );

  static ButtonStyle get secondaryButtonStyle => ElevatedButton.styleFrom(
        backgroundColor: bananaYellow,
        foregroundColor: earthBrown,
        textStyle: buttonStyle.copyWith(color: earthBrown),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
        elevation: 8,
      );

  // Number Button Style
  static ButtonStyle get numberButtonStyle => ElevatedButton.styleFrom(
        backgroundColor: white,
        foregroundColor: darkJungleGreen,
        textStyle: numberStyle,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(color: jungleGreen, width: 2),
        ),
        padding: const EdgeInsets.all(16),
        elevation: 4,
        minimumSize: const Size(60, 60),
      );
}
