import 'package:flutter/material.dart';
import '../models/multiplication_problem.dart';
import '../models/game_state.dart';
import '../services/audio_service.dart';

class GameProvider extends ChangeNotifier {
  GameState _gameState = GameState.idle;
  MultiplicationProblem? _currentProblem;
  int _score = 0;
  int _level = 1;
  int _streak = 0;
  int _totalProblems = 0;
  int _correctAnswers = 0;
  final List<int> _selectedNumbers = [];
  bool _isAnswerCorrect = false;
  String _feedbackMessage = '';
  final AudioService _audioService = AudioService();

  // Getters
  GameState get gameState => _gameState;
  MultiplicationProblem? get currentProblem => _currentProblem;
  int get score => _score;
  int get level => _level;
  int get streak => _streak;
  int get totalProblems => _totalProblems;
  int get correctAnswers => _correctAnswers;
  List<int> get selectedNumbers => _selectedNumbers;
  bool get isAnswerCorrect => _isAnswerCorrect;
  String get feedbackMessage => _feedbackMessage;
  double get accuracy => _totalProblems > 0 ? _correctAnswers / _totalProblems : 0.0;

  // Start a new game
  void startGame() async {
    _gameState = GameState.playing;
    _score = 0;
    _streak = 0;
    _totalProblems = 0;
    _correctAnswers = 0;
    _selectedNumbers.clear();

    // Initialize audio service
    await _audioService.initialize();

    generateNewProblem();
    notifyListeners();
  }

  // Generate a new multiplication problem
  void generateNewProblem() {
    final maxNumber = _getMaxNumberForLevel();
    _currentProblem = MultiplicationProblem.random(maxNumber);
    _selectedNumbers.clear();
    _isAnswerCorrect = false;
    _feedbackMessage = '';
    notifyListeners();
  }

  // Get maximum number based on current level
  int _getMaxNumberForLevel() {
    switch (_level) {
      case 1:
        return 5; // 1-5 tables
      case 2:
        return 8; // 1-8 tables
      case 3:
        return 10; // 1-10 tables
      case 4:
        return 12; // 1-12 tables
      default:
        return 12;
    }
  }

  // Select a number for multiplication
  void selectNumber(int number) async {
    if (_selectedNumbers.length < 2) {
      _selectedNumbers.add(number);

      // Play button tap sound
      await _audioService.playButtonTapSound();

      if (_selectedNumbers.length == 2) {
        _checkAnswer();
      }

      notifyListeners();
    }
  }

  // Remove a selected number
  void removeSelectedNumber(int number) {
    _selectedNumbers.remove(number);
    _isAnswerCorrect = false;
    _feedbackMessage = '';
    notifyListeners();
  }

  // Clear all selected numbers
  void clearSelectedNumbers() {
    _selectedNumbers.clear();
    _isAnswerCorrect = false;
    _feedbackMessage = '';
    notifyListeners();
  }

  // Check if the selected numbers form the correct answer
  void _checkAnswer() async {
    if (_selectedNumbers.length == 2 && _currentProblem != null) {
      final userAnswer = _selectedNumbers[0] * _selectedNumbers[1];
      final correctAnswer = _currentProblem!.answer;

      _totalProblems++;

      if (userAnswer == correctAnswer) {
        _isAnswerCorrect = true;
        _correctAnswers++;
        _streak++;
        _score += _calculateScore();
        _feedbackMessage = _generatePositiveFeedback();

        // Play success sound and speak the answer
        await _audioService.playCorrectSound();
        await _audioService.speakMultiplication(
          _selectedNumbers[0],
          _selectedNumbers[1],
          userAnswer
        );

        // Check for level up
        if (_streak >= 5 && _level < 4) {
          _levelUp();
        }

        // Play celebration for streaks
        if (_streak >= 3) {
          await _audioService.playCelebrationSound();
        }
      } else {
        _isAnswerCorrect = false;
        _streak = 0;
        _feedbackMessage = _generateNegativeFeedback();

        // Play incorrect sound and encouragement
        await _audioService.playIncorrectSound();
        await _audioService.speakEncouragement(_feedbackMessage);
      }

      notifyListeners();
    }
  }

  // Calculate score based on level and streak
  int _calculateScore() {
    int baseScore = 10 * _level;
    int streakBonus = _streak > 1 ? (_streak - 1) * 5 : 0;
    return baseScore + streakBonus;
  }

  // Generate positive feedback message
  String _generatePositiveFeedback() {
    final messages = [
      'Great job!',
      'Excellent!',
      'Well done!',
      'Perfect!',
      'Amazing!',
      'Fantastic!',
      'You\'re on fire!',
      'Keep it up!',
    ];
    return messages[_streak % messages.length];
  }

  // Generate negative feedback message
  String _generateNegativeFeedback() {
    final messages = [
      'Try again!',
      'Almost there!',
      'Keep practicing!',
      'You can do it!',
      'Don\'t give up!',
    ];
    return messages[_totalProblems % messages.length];
  }

  // Level up
  void _levelUp() async {
    _level++;
    _feedbackMessage = 'Level Up! Welcome to Level $_level!';
    await _audioService.speakLevelUp(_level);
  }

  // Get next problem
  void nextProblem() {
    generateNewProblem();
  }

  // Pause game
  void pauseGame() {
    _gameState = GameState.paused;
    notifyListeners();
  }

  // Resume game
  void resumeGame() {
    _gameState = GameState.playing;
    notifyListeners();
  }

  // End game
  void endGame() {
    _gameState = GameState.gameOver;
    notifyListeners();
  }

  // Reset game
  void resetGame() {
    _gameState = GameState.idle;
    _currentProblem = null;
    _score = 0;
    _level = 1;
    _streak = 0;
    _totalProblems = 0;
    _correctAnswers = 0;
    _selectedNumbers.clear();
    _isAnswerCorrect = false;
    _feedbackMessage = '';
    notifyListeners();
  }

  // Get speech text for current problem
  String getSpeechText() {
    if (_currentProblem != null && _selectedNumbers.length == 2) {
      return '${_selectedNumbers[0]} times ${_selectedNumbers[1]} is ${_selectedNumbers[0] * _selectedNumbers[1]}';
    }
    return '';
  }

  // Dispose resources
  @override
  void dispose() {
    _audioService.dispose();
    super.dispose();
  }
}
