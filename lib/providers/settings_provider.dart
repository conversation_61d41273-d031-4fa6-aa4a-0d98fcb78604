import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SettingsProvider extends ChangeNotifier {
  bool _soundEnabled = true;
  bool _isDarkMode = false;
  String _voiceGender = 'female'; // 'male' or 'female'
  double _voiceSpeed = 1.0;
  String _language = 'en';

  // Getters
  bool get soundEnabled => _soundEnabled;
  bool get isDarkMode => _isDarkMode;
  String get voiceGender => _voiceGender;
  double get voiceSpeed => _voiceSpeed;
  String get language => _language;

  SettingsProvider() {
    _loadSettings();
  }

  // Load settings from SharedPreferences
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _soundEnabled = prefs.getBool('sound_enabled') ?? true;
      _isDarkMode = prefs.getBool('is_dark_mode') ?? false;
      _voiceGender = prefs.getString('voice_gender') ?? 'female';
      _voiceSpeed = prefs.getDouble('voice_speed') ?? 1.0;
      _language = prefs.getString('language') ?? 'en';
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading settings: $e');
    }
  }

  // Save settings to SharedPreferences
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('sound_enabled', _soundEnabled);
      await prefs.setBool('is_dark_mode', _isDarkMode);
      await prefs.setString('voice_gender', _voiceGender);
      await prefs.setDouble('voice_speed', _voiceSpeed);
      await prefs.setString('language', _language);
    } catch (e) {
      debugPrint('Error saving settings: $e');
    }
  }

  // Toggle sound
  Future<void> toggleSound() async {
    _soundEnabled = !_soundEnabled;
    await _saveSettings();
    notifyListeners();
  }

  // Toggle dark mode
  Future<void> toggleDarkMode() async {
    _isDarkMode = !_isDarkMode;
    await _saveSettings();
    notifyListeners();
  }

  // Set voice gender
  Future<void> setVoiceGender(String gender) async {
    if (gender == 'male' || gender == 'female') {
      _voiceGender = gender;
      await _saveSettings();
      notifyListeners();
    }
  }

  // Set voice speed
  Future<void> setVoiceSpeed(double speed) async {
    if (speed >= 0.5 && speed <= 2.0) {
      _voiceSpeed = speed;
      await _saveSettings();
      notifyListeners();
    }
  }

  // Set language
  Future<void> setLanguage(String languageCode) async {
    _language = languageCode;
    await _saveSettings();
    notifyListeners();
  }

  // Reset to defaults
  Future<void> resetToDefaults() async {
    _soundEnabled = true;
    _isDarkMode = false;
    _voiceGender = 'female';
    _voiceSpeed = 1.0;
    _language = 'en';
    await _saveSettings();
    notifyListeners();
  }
}
