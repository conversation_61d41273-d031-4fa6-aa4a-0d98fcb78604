import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/reward_item.dart';

class RewardsProvider extends ChangeNotifier {
  int _bananas = 0;
  int _stars = 0;
  int _totalScore = 0;
  List<String> _unlockedSkins = ['default'];
  List<String> _unlockedBackgrounds = ['jungle_day'];
  String _currentSkin = 'default';
  String _currentBackground = 'jungle_day';
  List<RewardItem> _availableRewards = [];
  Map<String, int> _dailyProgress = {};

  // Getters
  int get bananas => _bananas;
  int get stars => _stars;
  int get totalScore => _totalScore;
  List<String> get unlockedSkins => _unlockedSkins;
  List<String> get unlockedBackgrounds => _unlockedBackgrounds;
  String get currentSkin => _currentSkin;
  String get currentBackground => _currentBackground;
  List<RewardItem> get availableRewards => _availableRewards;
  Map<String, int> get dailyProgress => _dailyProgress;

  RewardsProvider() {
    _initializeRewards();
    _loadRewards();
  }

  // Initialize available rewards
  void _initializeRewards() {
    _availableRewards = [
      // Monkey Skins
      RewardItem(
        id: 'pirate_hat',
        name: 'Pirate Hat',
        description: 'Ahoy! A swashbuckling monkey!',
        type: RewardType.skin,
        cost: 50,
        isUnlocked: false,
      ),
      RewardItem(
        id: 'wizard_hat',
        name: 'Wizard Hat',
        description: 'Magical multiplication powers!',
        type: RewardType.skin,
        cost: 75,
        isUnlocked: false,
      ),
      RewardItem(
        id: 'crown',
        name: 'Golden Crown',
        description: 'Fit for a math king!',
        type: RewardType.skin,
        cost: 100,
        isUnlocked: false,
      ),
      RewardItem(
        id: 'superhero_cape',
        name: 'Superhero Cape',
        description: 'Math superhero to the rescue!',
        type: RewardType.skin,
        cost: 125,
        isUnlocked: false,
      ),
      
      // Backgrounds
      RewardItem(
        id: 'jungle_night',
        name: 'Night Jungle',
        description: 'Mysterious nighttime jungle',
        type: RewardType.background,
        cost: 80,
        isUnlocked: false,
      ),
      RewardItem(
        id: 'beach_paradise',
        name: 'Beach Paradise',
        description: 'Tropical beach setting',
        type: RewardType.background,
        cost: 120,
        isUnlocked: false,
      ),
      RewardItem(
        id: 'space_adventure',
        name: 'Space Adventure',
        description: 'Math among the stars!',
        type: RewardType.background,
        cost: 150,
        isUnlocked: false,
      ),
    ];
  }

  // Load rewards from SharedPreferences
  Future<void> _loadRewards() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _bananas = prefs.getInt('bananas') ?? 0;
      _stars = prefs.getInt('stars') ?? 0;
      _totalScore = prefs.getInt('total_score') ?? 0;
      _unlockedSkins = prefs.getStringList('unlocked_skins') ?? ['default'];
      _unlockedBackgrounds = prefs.getStringList('unlocked_backgrounds') ?? ['jungle_day'];
      _currentSkin = prefs.getString('current_skin') ?? 'default';
      _currentBackground = prefs.getString('current_background') ?? 'jungle_day';
      
      // Load daily progress
      final today = DateTime.now().toIso8601String().split('T')[0];
      _dailyProgress = {
        today: prefs.getInt('daily_progress_$today') ?? 0,
      };
      
      // Update unlocked status for rewards
      for (var reward in _availableRewards) {
        if (reward.type == RewardType.skin) {
          reward.isUnlocked = _unlockedSkins.contains(reward.id);
        } else if (reward.type == RewardType.background) {
          reward.isUnlocked = _unlockedBackgrounds.contains(reward.id);
        }
      }
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading rewards: $e');
    }
  }

  // Save rewards to SharedPreferences
  Future<void> _saveRewards() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('bananas', _bananas);
      await prefs.setInt('stars', _stars);
      await prefs.setInt('total_score', _totalScore);
      await prefs.setStringList('unlocked_skins', _unlockedSkins);
      await prefs.setStringList('unlocked_backgrounds', _unlockedBackgrounds);
      await prefs.setString('current_skin', _currentSkin);
      await prefs.setString('current_background', _currentBackground);
      
      // Save daily progress
      final today = DateTime.now().toIso8601String().split('T')[0];
      if (_dailyProgress.containsKey(today)) {
        await prefs.setInt('daily_progress_$today', _dailyProgress[today]!);
      }
    } catch (e) {
      debugPrint('Error saving rewards: $e');
    }
  }

  // Add bananas
  Future<void> addBananas(int amount) async {
    _bananas += amount;
    await _saveRewards();
    notifyListeners();
  }

  // Add stars
  Future<void> addStars(int amount) async {
    _stars += amount;
    await _saveRewards();
    notifyListeners();
  }

  // Add to total score
  Future<void> addScore(int score) async {
    _totalScore += score;
    
    // Convert score to bananas (1 banana per 10 points)
    final bananasEarned = score ~/ 10;
    if (bananasEarned > 0) {
      await addBananas(bananasEarned);
    }
    
    // Update daily progress
    final today = DateTime.now().toIso8601String().split('T')[0];
    _dailyProgress[today] = (_dailyProgress[today] ?? 0) + score;
    
    await _saveRewards();
    notifyListeners();
  }

  // Purchase reward
  Future<bool> purchaseReward(String rewardId) async {
    final reward = _availableRewards.firstWhere(
      (r) => r.id == rewardId,
      orElse: () => throw Exception('Reward not found'),
    );

    if (_bananas >= reward.cost && !reward.isUnlocked) {
      _bananas -= reward.cost;
      reward.isUnlocked = true;
      
      if (reward.type == RewardType.skin) {
        _unlockedSkins.add(reward.id);
      } else if (reward.type == RewardType.background) {
        _unlockedBackgrounds.add(reward.id);
      }
      
      await _saveRewards();
      notifyListeners();
      return true;
    }
    
    return false;
  }

  // Set current skin
  Future<void> setCurrentSkin(String skinId) async {
    if (_unlockedSkins.contains(skinId)) {
      _currentSkin = skinId;
      await _saveRewards();
      notifyListeners();
    }
  }

  // Set current background
  Future<void> setCurrentBackground(String backgroundId) async {
    if (_unlockedBackgrounds.contains(backgroundId)) {
      _currentBackground = backgroundId;
      await _saveRewards();
      notifyListeners();
    }
  }

  // Get today's progress
  int getTodayProgress() {
    final today = DateTime.now().toIso8601String().split('T')[0];
    return _dailyProgress[today] ?? 0;
  }

  // Check if reward can be purchased
  bool canPurchase(String rewardId) {
    final reward = _availableRewards.firstWhere(
      (r) => r.id == rewardId,
      orElse: () => throw Exception('Reward not found'),
    );
    return _bananas >= reward.cost && !reward.isUnlocked;
  }
}
